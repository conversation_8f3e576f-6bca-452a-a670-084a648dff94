const { HealthCheck } = require('../models');
const { Logger } = require('../utils/logger');

/**
 * Health Check Handlers
 * Handles background jobs for system health monitoring
 */

/**
 * Check database health
 */
async function checkDatabaseHealth(jobData, job) {
  console.log('🔍 Checking database health...');
  
  try {
    const result = await HealthCheck.checkDatabaseHealth();
    
    console.log(`✅ Database health check completed: ${result.status}`);
    Logger.info('Database health check completed', result);

    return {
      success: true,
      health: result,
      message: `Database health: ${result.status}`,
    };

  } catch (error) {
    console.error('❌ Database health check failed:', error);
    Logger.error('Database health check failed', { error: error.message });
    throw error;
  }
}

/**
 * Check API health
 */
async function checkApiHealth(jobData, job) {
  console.log('🔍 Checking API health...');
  
  try {
    const startTime = Date.now();
    
    // Test internal API endpoints
    const endpoints = [
      '/health',
      '/api/test',
      '/api/v1/socket/status',
    ];

    const results = [];
    
    for (const endpoint of endpoints) {
      try {
        const endpointStartTime = Date.now();
        
        // Make internal request (you might need to adjust this based on your setup)
        const response = await fetch(`http://localhost:${process.env.PORT || 3001}${endpoint}`, {
          timeout: 5000,
        });
        
        const responseTime = Date.now() - endpointStartTime;
        
        results.push({
          endpoint,
          status: response.ok ? 'healthy' : 'unhealthy',
          statusCode: response.status,
          responseTime,
        });

      } catch (error) {
        results.push({
          endpoint,
          status: 'unhealthy',
          error: error.message,
          responseTime: null,
        });
      }
    }

    const totalResponseTime = Date.now() - startTime;
    const healthyCount = results.filter(r => r.status === 'healthy').length;
    const overallStatus = healthyCount === results.length ? 'healthy' : 
                         healthyCount > 0 ? 'degraded' : 'unhealthy';

    const healthResult = {
      status: overallStatus,
      responseTime: totalResponseTime,
      details: {
        endpoints: results,
        healthyEndpoints: healthyCount,
        totalEndpoints: results.length,
      },
    };

    // Record the health check
    await HealthCheck.recordCheck('api-health', overallStatus, totalResponseTime, healthResult.details);

    console.log(`✅ API health check completed: ${overallStatus}`);
    Logger.info('API health check completed', healthResult);

    return {
      success: true,
      health: healthResult,
      message: `API health: ${overallStatus}`,
    };

  } catch (error) {
    console.error('❌ API health check failed:', error);
    Logger.error('API health check failed', { error: error.message });
    
    // Record failed health check
    await HealthCheck.recordCheck('api-health', 'unhealthy', null, { error: error.message });
    
    throw error;
  }
}

/**
 * Check memory usage
 */
async function checkMemoryUsage(jobData, job) {
  console.log('🔍 Checking memory usage...');
  
  try {
    const result = await HealthCheck.checkMemoryUsage();
    
    console.log(`✅ Memory usage check completed: ${result.status}`);
    Logger.info('Memory usage check completed', result);

    return {
      success: true,
      health: result,
      message: `Memory usage: ${result.status}`,
    };

  } catch (error) {
    console.error('❌ Memory usage check failed:', error);
    Logger.error('Memory usage check failed', { error: error.message });
    throw error;
  }
}

/**
 * Check external services
 */
async function checkExternalServices(jobData, job) {
  console.log('🔍 Checking external services...');
  
  try {
    const startTime = Date.now();
    
    // Define external services to check
    const services = [
      {
        name: 'Google APIs',
        url: 'https://www.googleapis.com',
        timeout: 5000,
      },
      {
        name: 'Firebase',
        url: 'https://firebase.googleapis.com',
        timeout: 5000,
      },
      // Add more external services as needed
    ];

    const results = [];
    
    for (const service of services) {
      try {
        const serviceStartTime = Date.now();
        
        const response = await fetch(service.url, {
          method: 'HEAD',
          timeout: service.timeout,
        });
        
        const responseTime = Date.now() - serviceStartTime;
        
        results.push({
          name: service.name,
          url: service.url,
          status: response.ok ? 'healthy' : 'degraded',
          statusCode: response.status,
          responseTime,
        });

      } catch (error) {
        results.push({
          name: service.name,
          url: service.url,
          status: 'unhealthy',
          error: error.message,
          responseTime: null,
        });
      }
    }

    const totalResponseTime = Date.now() - startTime;
    const healthyCount = results.filter(r => r.status === 'healthy').length;
    const overallStatus = healthyCount === results.length ? 'healthy' : 
                         healthyCount > 0 ? 'degraded' : 'unhealthy';

    const healthResult = {
      status: overallStatus,
      responseTime: totalResponseTime,
      details: {
        services: results,
        healthyServices: healthyCount,
        totalServices: results.length,
      },
    };

    // Record the health check
    await HealthCheck.recordCheck('external-services', overallStatus, totalResponseTime, healthResult.details);

    console.log(`✅ External services check completed: ${overallStatus}`);
    Logger.info('External services check completed', healthResult);

    return {
      success: true,
      health: healthResult,
      message: `External services: ${overallStatus}`,
    };

  } catch (error) {
    console.error('❌ External services check failed:', error);
    Logger.error('External services check failed', { error: error.message });
    
    // Record failed health check
    await HealthCheck.recordCheck('external-services', 'unhealthy', null, { error: error.message });
    
    throw error;
  }
}

/**
 * Check disk space
 */
async function checkDiskSpace(jobData, job) {
  console.log('🔍 Checking disk space...');
  
  try {
    const fs = require('fs').promises;
    const path = require('path');
    
    const startTime = Date.now();
    
    // Get disk space info (this is a simplified version)
    const stats = await fs.stat(process.cwd());
    
    // Note: Getting actual disk space requires platform-specific code
    // This is a placeholder implementation
    const diskInfo = {
      available: 'unknown', // Would need platform-specific implementation
      used: 'unknown',
      total: 'unknown',
      usagePercent: 0,
    };

    const responseTime = Date.now() - startTime;
    const status = 'healthy'; // Would be based on actual disk usage

    const healthResult = {
      status,
      responseTime,
      details: diskInfo,
    };

    // Record the health check
    await HealthCheck.recordCheck('disk-space', status, responseTime, diskInfo);

    console.log(`✅ Disk space check completed: ${status}`);
    Logger.info('Disk space check completed', healthResult);

    return {
      success: true,
      health: healthResult,
      message: `Disk space: ${status}`,
    };

  } catch (error) {
    console.error('❌ Disk space check failed:', error);
    Logger.error('Disk space check failed', { error: error.message });
    
    // Record failed health check
    await HealthCheck.recordCheck('disk-space', 'unhealthy', null, { error: error.message });
    
    throw error;
  }
}

/**
 * Check frontend health
 */
async function checkFrontendHealth(jobData, job) {
  console.log('🔍 Checking frontend health...');

  try {
    const startTime = Date.now();

    // Frontend URLs to check
    const frontendUrls = [
      {
        name: 'Frontend Home',
        url: process.env.FRONTEND_URL || 'http://localhost:5174',
        timeout: 10000,
      }
    ];

    const results = [];

    for (const frontend of frontendUrls) {
      try {
        const frontendStartTime = Date.now();

        const response = await fetch(frontend.url, {
          method: 'GET',
          timeout: frontend.timeout,
          headers: {
            'User-Agent': 'HLenergy-HealthCheck/1.0'
          }
        });

        const responseTime = Date.now() - frontendStartTime;

        results.push({
          name: frontend.name,
          url: frontend.url,
          status: response.ok ? 'healthy' : 'degraded',
          statusCode: response.status,
          responseTime,
        });

      } catch (error) {
        // If it's an optional endpoint and fails, mark as degraded instead of unhealthy
        const status = frontend.optional ? 'degraded' : 'unhealthy';

        results.push({
          name: frontend.name,
          url: frontend.url,
          status,
          error: error.message,
          responseTime: null,
        });
      }
    }

    const totalResponseTime = Date.now() - startTime;
    const healthyCount = results.filter(r => r.status === 'healthy').length;
    const degradedCount = results.filter(r => r.status === 'degraded').length;

    // Determine overall status
    let overallStatus;
    if (healthyCount === results.length) {
      overallStatus = 'healthy';
    } else if (healthyCount > 0 || degradedCount > 0) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'unhealthy';
    }

    const healthResult = {
      status: overallStatus,
      responseTime: totalResponseTime,
      details: {
        frontends: results,
        healthyFrontends: healthyCount,
        degradedFrontends: degradedCount,
        totalFrontends: results.length,
      },
    };

    // Record the health check
    await HealthCheck.recordCheck('frontend-health', overallStatus, totalResponseTime, healthResult.details);

    console.log(`✅ Frontend health check completed: ${overallStatus}`);
    Logger.info('Frontend health check completed', healthResult);

    return {
      success: true,
      health: healthResult,
      message: `Frontend health: ${overallStatus}`,
    };

  } catch (error) {
    console.error('❌ Frontend health check failed:', error);
    Logger.error('Frontend health check failed', { error: error.message });

    // Record failed health check
    await HealthCheck.recordCheck('frontend-health', 'unhealthy', null, { error: error.message });

    throw error;
  }
}

/**
 * Comprehensive system health check
 * Checks database, API, and frontend health, sends alerts if issues found
 */
async function comprehensiveHealthCheck(jobData, job) {
  console.log('🏥 Running comprehensive system health check...');

  try {
    const startTime = Date.now();
    const healthResults = {};
    const issues = [];

    // Run all health checks in parallel
    const [dbHealth, apiHealth, frontendHealth] = await Promise.allSettled([
      checkDatabaseHealth({}, job),
      checkApiHealth({}, job),
      checkFrontendHealth({}, job)
    ]);

    // Process database health results
    if (dbHealth.status === 'fulfilled') {
      healthResults.database = dbHealth.value.health;
      if (dbHealth.value.health.status !== 'healthy') {
        issues.push({
          component: 'Database',
          status: dbHealth.value.health.status,
          details: dbHealth.value.health.details || {},
          severity: dbHealth.value.health.status === 'unhealthy' ? 'critical' : 'warning'
        });
      }
    } else {
      healthResults.database = { status: 'unhealthy', error: dbHealth.reason.message };
      issues.push({
        component: 'Database',
        status: 'unhealthy',
        details: { error: dbHealth.reason.message },
        severity: 'critical'
      });
    }

    // Process API health results
    if (apiHealth.status === 'fulfilled') {
      healthResults.api = apiHealth.value.health;
      if (apiHealth.value.health.status !== 'healthy') {
        issues.push({
          component: 'API',
          status: apiHealth.value.health.status,
          details: apiHealth.value.health.details || {},
          severity: apiHealth.value.health.status === 'unhealthy' ? 'critical' : 'warning'
        });
      }
    } else {
      healthResults.api = { status: 'unhealthy', error: apiHealth.reason.message };
      issues.push({
        component: 'API',
        status: 'unhealthy',
        details: { error: apiHealth.reason.message },
        severity: 'critical'
      });
    }

    // Process frontend health results
    if (frontendHealth.status === 'fulfilled') {
      healthResults.frontend = frontendHealth.value.health;
      if (frontendHealth.value.health.status !== 'healthy') {
        issues.push({
          component: 'Frontend',
          status: frontendHealth.value.health.status,
          details: frontendHealth.value.health.details || {},
          severity: frontendHealth.value.health.status === 'unhealthy' ? 'high' : 'medium'
        });
      }
    } else {
      healthResults.frontend = { status: 'unhealthy', error: frontendHealth.reason.message };
      issues.push({
        component: 'Frontend',
        status: 'unhealthy',
        details: { error: frontendHealth.reason.message },
        severity: 'high'
      });
    }

    // Determine overall system health
    const allStatuses = [
      healthResults.database.status,
      healthResults.api.status,
      healthResults.frontend.status
    ];

    let overallStatus;
    if (allStatuses.every(status => status === 'healthy')) {
      overallStatus = 'healthy';
    } else if (allStatuses.some(status => status === 'unhealthy')) {
      overallStatus = 'unhealthy';
    } else {
      overallStatus = 'degraded';
    }

    const totalResponseTime = Date.now() - startTime;

    const comprehensiveResult = {
      status: overallStatus,
      responseTime: totalResponseTime,
      timestamp: new Date().toISOString(),
      components: healthResults,
      issues: issues,
      summary: {
        totalComponents: 3,
        healthyComponents: allStatuses.filter(s => s === 'healthy').length,
        degradedComponents: allStatuses.filter(s => s === 'degraded').length,
        unhealthyComponents: allStatuses.filter(s => s === 'unhealthy').length,
      }
    };

    // Record the comprehensive health check
    await HealthCheck.recordCheck('comprehensive-health', overallStatus, totalResponseTime, comprehensiveResult);

    // Send email notifications if there are issues
    if (issues.length > 0) {
      console.log(`⚠️ Found ${issues.length} health issues, sending email notification...`);

      try {
        const { BackgroundJob } = require('../models');

        // Enqueue email notification job
        await BackgroundJob.enqueue('send-alert-notification', {
          alertType: 'system_health_alert',
          message: `System health check found ${issues.length} issue(s)`,
          severity: issues.some(i => i.severity === 'critical') ? 'critical' :
                   issues.some(i => i.severity === 'high') ? 'high' : 'medium',
          details: {
            overallStatus,
            issues,
            healthResults,
            timestamp: new Date().toISOString(),
            checkDuration: totalResponseTime
          }
        }, { priority: 8 }); // High priority for health alerts

        console.log('✅ Health alert email queued successfully');
        Logger.info('Health alert email queued', { issueCount: issues.length, severity: issues[0]?.severity });

      } catch (emailError) {
        console.error('❌ Failed to queue health alert email:', emailError);
        Logger.error('Failed to queue health alert email', { error: emailError.message });
      }
    } else {
      console.log('✅ All systems healthy - no alerts needed');
    }

    console.log(`✅ Comprehensive health check completed: ${overallStatus} (${issues.length} issues)`);
    Logger.info('Comprehensive health check completed', {
      status: overallStatus,
      issueCount: issues.length,
      responseTime: totalResponseTime
    });

    return {
      success: true,
      health: comprehensiveResult,
      message: `Comprehensive health check: ${overallStatus} (${issues.length} issues found)`,
    };

  } catch (error) {
    console.error('❌ Comprehensive health check failed:', error);
    Logger.error('Comprehensive health check failed', { error: error.message });

    // Record failed health check
    await HealthCheck.recordCheck('comprehensive-health', 'unhealthy', null, { error: error.message });

    throw error;
  }
}

module.exports = {
  checkDatabaseHealth,
  checkApiHealth,
  checkMemoryUsage,
  checkExternalServices,
  checkDiskSpace,
  checkFrontendHealth,
  comprehensiveHealthCheck,
};
