# CRM Dashboard API Requirements

## 📋 **Required API Endpoints**

### **1. Customer Endpoints**

```http
GET /api/customers
```
**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "firstName": "<PERSON>",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "company": "ABC Corp",
      "status": "active",
      "priority": "high",
      "createdAt": "2024-01-15T10:30:00Z",
      "lastContactDate": "2024-01-20T14:15:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 50
}
```

### **2. Project Endpoints**

```http
GET /api/projects
GET /api/projects?customerId=123
```
**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "projectNumber": "PRJ-2024-001",
      "title": "Solar Installation",
      "description": "Residential solar panel installation",
      "customerId": 123,
      "type": "solar_installation",
      "status": "in_progress",
      "priority": "high",
      "progress": 65,
      "startDate": "2024-01-15",
      "endDate": "2024-03-15",
      "budget": 25000,
      "createdAt": "2024-01-10T09:00:00Z"
    }
  ]
}
```

### **3. Document Endpoints**

```http
GET /api/documents
GET /api/documents?customerId=123&projectId=456&type=contract
```
**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "contract_signed.pdf",
      "type": "contract",
      "size": 2048576,
      "mimeType": "application/pdf",
      "url": "/api/documents/1/download",
      "customerId": 123,
      "customerName": "John Doe",
      "projectId": 456,
      "projectName": "Solar Installation",
      "uploadedAt": "2024-01-15T10:30:00Z",
      "downloadCount": 5,
      "lastAccessed": "2024-01-20T14:15:00Z",
      "description": "Signed installation contract",
      "isPublic": false
    }
  ]
}
```

```http
POST /api/documents/upload
```
**Payload (FormData):**
```javascript
{
  files: [File, File, ...],
  customerId: 123,
  projectId: 456, // optional
  type: "contract", // optional
  description: "Contract documents" // optional
}
```
**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "contract.pdf",
      "url": "/api/documents/1/download",
      "size": 2048576,
      "type": "contract"
    }
  ]
}
```

```http
GET /api/documents/{id}/download
```
**Response:** File stream with appropriate headers

```http
PUT /api/documents/{id}
```
**Payload:**
```json
{
  "name": "updated_contract.pdf",
  "type": "contract",
  "description": "Updated contract with amendments",
  "customerId": 123,
  "projectId": 456,
  "isPublic": false
}
```

```http
POST /api/documents/{id}/share
```
**Payload:**
```json
{
  "email": "<EMAIL>",
  "message": "Please review the attached document",
  "permissions": "view",
  "expiryDate": "2024-02-15"
}
```

```http
DELETE /api/documents/{id}
```
**Response:**
```json
{
  "success": true,
  "message": "Document deleted successfully"
}
```

### **4. Dashboard Statistics**

```http
GET /api/dashboard/stats
```
**Response:**
```json
{
  "totalCustomers": 150,
  "activeProjects": 23,
  "highPriorityCustomers": 8,
  "unreadCommunications": 12,
  "recentActivities": [
    {
      "id": 1,
      "type": "document_uploaded",
      "description": "Contract uploaded for John Doe",
      "userName": "Jane Smith",
      "createdAt": "2024-01-20T14:30:00Z",
      "avatar": "/avatars/jane.jpg"
    }
  ]
}
```

### **5. Communication Endpoints**

```http
GET /api/communications?customerId=123&limit=5
```
**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "customerId": 123,
      "type": "email",
      "subject": "Project Update",
      "content": "Your solar installation is progressing well...",
      "createdAt": "2024-01-20T10:00:00Z",
      "createdBy": 2,
      "createdByName": "Jane Smith"
    }
  ]
}
```

### **6. File Management**

```http
POST /api/documents/{id}/duplicate
```
**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Copy of contract.pdf",
    "url": "/api/documents/2/download"
  }
}
```

```http
POST /api/documents/export
```
**Payload:**
```json
{
  "format": "csv",
  "filters": {
    "customerId": 123,
    "type": "contract",
    "dateFrom": "2024-01-01",
    "dateTo": "2024-01-31"
  }
}
```

### **7. Search & Filter**

```http
GET /api/customers/search?q=john&status=active&priority=high
GET /api/projects/search?q=solar&status=in_progress
GET /api/documents/search?q=contract&type=contract&customerId=123
```

## 🔧 **Error Response Format**

All endpoints should return errors in this format:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Customer ID is required",
    "details": {
      "field": "customerId",
      "value": null
    }
  }
}
```

## 📊 **Pagination Format**

For paginated endpoints:
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 150,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 📝 **Additional Notes**

### **Document Types**
- `contract` - Legal contracts and agreements
- `proposal` - Project proposals and quotes
- `invoice` - Billing and payment documents
- `report` - Technical reports and assessments
- `other` - Miscellaneous documents

### **Project Types**
- `energy_audit` - Energy efficiency assessments
- `solar_installation` - Solar panel installations
- `maintenance` - Equipment maintenance and repairs
- `consultation` - Advisory services
- `retrofit` - Building retrofits and upgrades
- `monitoring` - System monitoring and analytics

### **Customer Status**
- `active` - Active customers with ongoing projects
- `inactive` - Dormant customers
- `lead` - Potential customers
- `prospect` - Qualified leads

### **Priority Levels**
- `low` - Standard priority
- `medium` - Elevated priority
- `high` - Urgent attention required

### **Project Status**
- `planning` - Project in planning phase
- `in_progress` - Active project execution
- `on_hold` - Temporarily paused
- `completed` - Successfully finished
- `cancelled` - Project terminated

### **Communication Types**
- `email` - Email communications
- `phone` - Phone calls
- `meeting` - In-person or virtual meetings
- `note` - Internal notes and observations
- `sms` - Text message communications

These endpoints provide all the data needed for the CRMDashboard component to function fully with real backend data instead of the current mock data.
